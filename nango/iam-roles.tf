locals {
  roles = {
    default_instance_role = {
      role_requires_mfa = false

      trusted_role_services = [
        "ec2.amazonaws.com"
      ]
      custom_role_policy_arns = [
        "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
        # "${module.iam_policy_from_data_source["ProdAppS3Access"].arn}"

      ]
    }
  }
}

module "iam_assumable_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-assumable-role"
  version = "5.18.0"

  for_each = local.roles

  role_name                = each.key
  create_role              = true
  trusted_role_services    = try(each.value.trusted_role_services, [])
  custom_role_trust_policy = try(each.value.custom_role_trust_policy, "")
  role_requires_mfa        = try(each.value.role_requires_mfa, false)
  custom_role_policy_arns  = try(each.value.custom_role_policy_arns, [])
}
