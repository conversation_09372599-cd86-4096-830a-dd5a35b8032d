locals {
  iam-service-linked-roles = {
    "rds.amazonaws.com" = {
      description = "Allows Amazon RDS to manage AWS resources on your behalf"
    }
    "ecs.amazonaws.com" = {
      description = "Role to enable Amazon ECS to manage your cluster."
    }
  }
}

###############################################################################
# Service Linked Roles
###############################################################################
resource "aws_iam_service_linked_role" "this" {
  for_each = local.iam-service-linked-roles

  aws_service_name = each.key
  description      = try(each.value.description, "")
}
