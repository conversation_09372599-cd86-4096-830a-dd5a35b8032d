################################################################################
################################################################################
# Data Objects
################################################################################
################################################################################

data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

################################################################################
# Latest Ubuntu 20.04 AMI
################################################################################
data "aws_ami" "ubuntu_20_04" {
  most_recent = true
  owners      = ["099720109477"]

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server*"]
  }
}

################################################################################
# IAM objects
################################################################################
# RDS IAM Role Trust Policy
data "aws_iam_policy_document" "rds_custom_trust_policy" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    condition {
      test     = "StringEquals"
      variable = "aws:SourceArn"
      values   = ["${module.aurora_cluster["prod"].cluster_arn}"]
    }

    principals {
      type        = "Service"
      identifiers = ["rds.amazonaws.com"]
    }
  }
}

# ProdS3Access
data "aws_iam_policy_document" "ProdS3Access" {
  statement {
    actions = [
      "s3:ListAllMyBuckets",
      "s3:GetBucketLocation",
    ]

    resources = [
      "arn:aws:s3:::*",
    ]
  }
  statement {
    actions = [
      "s3:ListBucket",
    ]

    resources = [
      "arn:aws:s3:::${module.s3_bucket["et-nango-prod"].s3_bucket_id}",
    ]
  }
  statement {
    actions = [
      "s3:*",
    ]

    resources = [
      "arn:aws:s3:::${module.s3_bucket["et-nango-prod"].s3_bucket_id}",
      "arn:aws:s3:::${module.s3_bucket["et-nango-prod"].s3_bucket_id}/*",
    ]
  }
}

################################################################################
# SSL Private keys
###############################################################################
data "aws_secretsmanager_secret_version" "nango_evertrue_com_acme_reg_private_key_current" {
  secret_id = module.secrets-manager["nango_evertrue_com_acme_reg_private_key"].secret_ids.nango_evertrue_com_acme_reg_private_key
}

data "aws_secretsmanager_secret_version" "nango_evertrue_com_cert_private_key_current" {
  secret_id = module.secrets-manager["nango_evertrue_com_cert_private_key"].secret_ids.nango_evertrue_com_cert_private_key
}

################################################################################
# SSL Public keys
################################################################################
data "tls_public_key" "nango_evertrue_com_acme_reg_public_key_current" {
  private_key_pem = jsondecode(data.aws_secretsmanager_secret_version.nango_evertrue_com_acme_reg_private_key_current.secret_string)["reg_key"]
}
