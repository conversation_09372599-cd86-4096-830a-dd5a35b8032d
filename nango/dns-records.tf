locals {
  template_records = {
    # # Example
    # # "record" = {
    # #   name = "",
    # #   records = [
    # #     ""
    # #   ],
    # #   type = "",
    # #   ttl  = "60"
    # # }
  }
}
module "dns_records" {
  source  = "terraform-aws-modules/route53/aws//modules/records"
  version = "~> 2.0"

  for_each = local.template_records

  records = [each.value]
  #zone_name = lookup(module.dns_zones["pledgemine.com"].route53_zone_name, "pledgemine.com", "effin' DRAGONS mate!")
}
