locals {
  # availability_zone_subnets = {
  #   for s in data.aws_subnet.private : s.availability_zone => s.id...
  # }
  log_groups = {
    "nango-server" = {
      retention_in_days = 7
      log_group_class   = "STANDARD"

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "Nango Server Task Log Group", CreateDate = "20240214", "AmazonECSManaged" = "" })}"
    }
    "nango-jobs" = {
      retention_in_days = 7
      log_group_class   = "STANDARD"

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "Nango Jobs Task Log Group", CreateDate = "20240214", "AmazonECSManaged" = "" })}"
    }
    "nango-persist" = {
      retention_in_days = 7
      log_group_class   = "STANDARD"

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "Nango Persist Task Log Group", CreateDate = "20240214", "AmazonECSManaged" = "" })}"
    }
    "nango-runner" = {
      retention_in_days = 7
      log_group_class   = "STANDARD"

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "Nango Runner Task Log Group", CreateDate = "20240214", "AmazonECSManaged" = "" })}"
    }
    "nango-orchestrator" = {
      retention_in_days = 7
      log_group_class   = "STANDARD"

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "Nango Orchestrator Task Log Group", CreateDate = "20240214", "AmazonECSManaged" = "" })}"
    }
  }
}

module "log_group" {
  source  = "terraform-aws-modules/cloudwatch/aws//modules/log-group"
  version = "~> 5.0"

  for_each = local.log_groups

  name              = each.key
  retention_in_days = try(each.value.retention_in_days, 7)
  kms_key_id        = try(each.value.kms_key_id, null)
  log_group_class   = try(each.value.log_group_class, "INFREQUENT_ACCESS")
  tags              = try(each.value.tags, {})
}
