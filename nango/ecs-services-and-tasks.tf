locals {
  ecs_service = {
    nango-server = {
      # Service Definition
      scheduling_strategy = "REPLICA"
      deployment_circuit_breaker = {
        enable   = true
        rollback = true
      }
      deployment_controller = {
        type = "ECS"
      }
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      desired_count                      = 1
      health_check_grace_period_seconds  = 0
      iam_role_arn                       = aws_iam_service_linked_role.this["ecs.amazonaws.com"].arn
      load_balancer = {
        service = {
          target_group_arn = module.alb["nango"].target_group_arns[0]
          container_name   = "nango-server"
          container_port   = 8080
          #container_port   = 3003
        }
      }
      enable_autoscaling       = true
      autoscaling_min_capacity = 1
      autoscaling_max_capacity = 1
      autoscaling_policies = {
        cpu = {
          policy_type = "TargetTrackingScaling"

          target_tracking_scaling_policy_configuration = {
            predefined_metric_specification = {
              predefined_metric_type = "ECSServiceAverageCPUUtilization"
            }
            scale_in_cooldown  = 60
            scale_out_cooldown = 300
            target_value       = 70
          }
        }
      }
      volume = {
        secrets = {
          efs_volume_configuration = {
            file_system_id     = module.efs["nango_secrets"].id
            root_directory     = "/secrets"
            transit_encryption = "ENABLED"
          }

        }
      }
      security_group_ids = [module.security_group["prod-self"].security_group_id]
      # Task Definition
      requires_compatibilities = ["FARGATE"]
      family                   = "nango-server"
      enable_execute_command   = true   #added to enable exec into container
      container_definitions = {
        nango-server = {
          image = "docker.io/nangohq/nango:0e82ec7811ffb939367a543e386353d06bde499a"
          entrypoint = ["packages/server/entrypoint.sh"]
          portMappings = [
            {
              #name          = "nango-server-3003-tcp"
              name          = "nango-server-8080-tcp"
              #containerPort = 3003
              containerPort = 8080
              protocol      = "tcp"
              appProtocol   = "http"
              #hostPort      = 3003
              hostPort      = 8080
            }
          ]
          logConfiguration = {
            logDriver = "awslogs",
            options = {
              awslogs-group         = module.log_group["nango-server"].cloudwatch_log_group_name
              awslogs-region        = "us-east-1"
              awslogs-stream-prefix = "ecs"
            }
          }
          essential = true
          secrets = [
            {
              name      = "NANGO_DB_PASSWORD"
              valueFrom = "${module.secrets-manager["nango_db_user_password"].secret_arns.nango_db_user_password}:nango_db_user_password::"
            },
            {
              name      = "RECORDS_DATABASE_URL"
              valueFrom = "${module.secrets-manager["nango_db_user_password"].secret_arns.nango_db_user_password}:records_database_url::"
            },
            {
              name      = "NANGO_ENCRYPTION_KEY"
              valueFrom = "${module.secrets-manager["nango_secrets"].secret_arns.nango_secrets}:nango_encryption_key::"
            },
            {
              name      = "DD_API_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:datadog-api-key-kGP4f7:api_key::"
            },
            {
              name      = "DD_APP_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:data-dog-app-key-949kBz:data-dog-app-key::"
            }

          ]
          environment = [
            {
              name  = "AWS_REGION",
              value = data.aws_region.current.name
            },
            {
              name  = "AWS_BUCKET_NAME",
              value = module.s3_bucket["et-nango-prod"].s3_bucket_id
            },
            {
              name  = "NANGO_DB_HOST",
              value = module.aurora_cluster["prod"].cluster_endpoint
            },
            {
              name  = "NANGO_DB_NAME",
              value = "nango"
            },
            {
              name  = "NANGO_DB_USER"
              value = "nango"
            },
            {
              name  = "NANGO_DB_PORT"
              value = 5432
            },
            {
              name  = "NANGO_DB_SSL"
              value = false
            },
            {
              name  = "NANGO_SERVER_URL"
              value = "https://nango.evertrue.com"
            },
            {
              name  = "NANGO_CALLBACK_URL"
              value = "https://nango.evertrue.com/oauth/callback"
            },
            {
              name  = "TEMPORAL_ADDRESS"
              value = "evertrue-flows.guqns.tmprl.cloud:7233"
            },
            {
              name  = "TEMPORAL_NAMESPACE"
              value = "evertrue-flows.guqns"
            },
            {
              name  = "NODE_ENV"
              value = "production"
            },
            {
              name  = "NANGO_ENTERPRISE"
              value = "true"
            },
            {
              name  = "MAILGUN_API_KEY"
              value = "**************************************************"
            },
            {
              name  = "PERSIST_SERVICE_URL"
              value = "http://${module.alb["nango-persist"].lb_dns_name}"
            },
            {
              name  = "JOBS_SERVICE_URL"
              value = "http://${module.alb["nango-jobs"].lb_dns_name}"
            },
            {
              name  = "RUNNER_SERVICE_URL"
              value = "http://${module.alb["nango-runner"].lb_dns_name}"
            },
            {
              name  = "ORCHESTRATOR_SERVICE_URL"
              value = "http://${module.alb["nango-orchestrator"].lb_dns_name}"
            },
            {
              name  = "NANGO_ADMIN_UUID"
              value = "25bf0b16-54a3-49e6-aec0-fd4b514c0b2e"
            },
            {
              name  = "NANGO_LOGS_ENABLED"
              value = "true"
            },
            {
              name  = "NANGO_LOGS_ES_PWD"
              value = "lyJbF5Q8fzCd2xiiRb4FMdOz"
            },
            {
              name  = "NANGO_LOGS_ES_URL"
              value = "https://nango-evertrue.es.us-west-2.aws.found.io/"
            },
            {
              name  = "NANGO_LOGS_ES_USER"
              value = "elastic"
            },
            #{
              #name  = "FLAG_SERVE_CONNECT_UI"
              #value = "true"
            #},
            {
              name  = "NANGO_PUBLIC_CONNECT_URL"
              value = "https://nango.evertrue.com:3009"

            },
  # Datadog environment variables for metrics ####################
            {
              name  = "DD_AGENT_HOST"
              value = "datadog-agent.nango-services"
            },
            {
              name  = "DD_DOGSTATSD_PORT"
              value = "8125"
            },
            {
              name  = "DD_ENV"
              value = "production"
            },
            {
              name  = "DD_SERVICE"
              value = "nango-server"
            },
            {
              name  = "DD_VERSION"
              value = "v1.0.0"
            },
            {
              name  = "DD_PROFILING_ENABLED"
              value = "true"
            },
            {
              name  = "DD_DOGSTATSD_NON_LOCAL_TRAFFIC"
              value = "true"
            },
            {
              name  = "DD_METRICS_ENABLED"
              value = "true"
            },
            {
              name = "DD_SITE",
              value = "datadoghq.com"
            },
            {
              name  = "FLAG_SERVE_CONNECT_UI"
              value = "true"
            },
            {
              name = "NANGO_SERVER_PUBLIC_BODY_LIMIT"
              value = "250mb"
            },
            {
              name = "NANGO_LOGS_ES_PREFIX"
              value = "prod"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_OPERATIONS"
              value = "prod-operations"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_MESSAGES"
              value = "prod-messages"
            }

          ]
          mountPoints = [
            {
              sourceVolume  = "secrets",
              containerPath = "/etc/secrets/",
              readOnly      = false
            }
          ]
          readonlyRootFilesystem = false
        }
      }
      cpu    = 1024
      memory = 2048
      runtime_platform = {
        cpu_architecture        = "X86_64"
        operating_system_family = "LINUX"
      }
      network_mode               = "awsvpc"
      create_tasks_iam_role      = true
      tasks_iam_role_name        = "nango-server-prod-task"
      tasks_iam_role_description = "These permissions are assumed by the containers running in the task"
      tasks_iam_role_policies = {
        AmazonECSTaskExecutionRolePolicy = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
        S3ProdBucketAccess               = "${module.iam_policy_from_data_source["ProdS3Access"].arn}"
      }
      create_task_exec_iam_role      = true
      task_exec_iam_role_name        = "nango-server-prod-task-exec"
      task_exec_iam_role_description = "These permissions are used by ECS to run the task"
      task_exec_iam_statements = [
        {
          sid    = "GetSecrets"
          effect = "Allow"
          actions = [
            "secretsmanager:GetSecretValue"
          ]
          resources = ["arn:aws:secretsmanager:*:*:secret:*"]
        }
      ]

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "ECS Cluster Nango Server Service", CreateDate = "20240214", })}"
    }
    nango-jobs = {
      # Service Definition
      scheduling_strategy = "REPLICA"
      deployment_circuit_breaker = {
        enable   = true
        rollback = true
      }
      deployment_controller = {
        type = "ECS"
      }
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      desired_count                      = 1
      health_check_grace_period_seconds  = 0
      iam_role_arn                       = aws_iam_service_linked_role.this["ecs.amazonaws.com"].arn
      load_balancer = {
        service = {
          target_group_arn = module.alb["nango-jobs"].target_group_arns[0]
          container_name   = "nango-jobs"
          container_port   = 3005
        }
      }
      enable_autoscaling       = true
      autoscaling_min_capacity = 1
      autoscaling_max_capacity = 1
      autoscaling_policies = {
        cpu = {
          policy_type = "TargetTrackingScaling"

          target_tracking_scaling_policy_configuration = {
            predefined_metric_specification = {
              predefined_metric_type = "ECSServiceAverageCPUUtilization"
            }
            scale_in_cooldown  = 60
            scale_out_cooldown = 300
            target_value       = 70
          }
        }
      }
      volume = {
        secrets = {
          efs_volume_configuration = {
            file_system_id     = module.efs["nango_secrets"].id
            root_directory     = "/secrets"
            transit_encryption = "ENABLED"
          }

        }
      }
      security_group_ids = [module.security_group["prod-self"].security_group_id]
      # Task Definition
      requires_compatibilities = ["FARGATE"]
      family                   = "nango-jobs"
      enable_execute_command   = true   #added to enable exec into container
      container_definitions = {
        nango-jobs = {
          image   = "docker.io/nangohq/nango:0e82ec7811ffb939367a543e386353d06bde499a"
          command = ["node", "packages/jobs/dist/app.js"]
          portMappings = [
            {
              name          = "nango-jobs-3005-tcp"
              containerPort = 3005
              protocol      = "tcp"
              appProtocol   = "http"
              hostPort      = 3005
            }
          ]
          logConfiguration = {
            logDriver = "awslogs",
            options = {
              awslogs-group         = module.log_group["nango-jobs"].cloudwatch_log_group_name
              awslogs-region        = "us-east-1"
              awslogs-stream-prefix = "ecs"
            }
          }
          essential = true
          secrets = [
            {
              name      = "NANGO_DB_PASSWORD"
              valueFrom = "${module.secrets-manager["nango_db_user_password"].secret_arns.nango_db_user_password}:nango_db_user_password::"
            },
            {
              name      = "RECORDS_DATABASE_URL"
              valueFrom = "${module.secrets-manager["nango_db_user_password"].secret_arns.nango_db_user_password}:records_database_url::"
            },
            {
              name      = "NANGO_ENCRYPTION_KEY"
              valueFrom = "${module.secrets-manager["nango_secrets"].secret_arns.nango_secrets}:nango_encryption_key::"
            },
            {
              name      = "DD_API_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:datadog-api-key-kGP4f7:api_key::"
            },
            {
              name      = "DD_APP_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:data-dog-app-key-949kBz:data-dog-app-key::"
            }
          ]
          environment = [
            {
              name  = "AWS_REGION",
              value = data.aws_region.current.name
            },
            {
              name  = "AWS_BUCKET_NAME",
              value = module.s3_bucket["et-nango-prod"].s3_bucket_id
            },
            {
              name  = "NANGO_DB_HOST",
              value = module.aurora_cluster["prod"].cluster_endpoint
            },
            {
              name  = "NANGO_DB_NAME",
              value = "nango"
            },
            {
              name  = "NANGO_DB_USER"
              value = "nango"
            },
            {
              name  = "NANGO_DB_PORT"
              value = 5432
            },
            {
              name  = "NANGO_DB_SSL"
              value = false
            },
            {
              name  = "NANGO_SERVER_URL"
              value = "https://nango.evertrue.com"
            },
            {
              name  = "NANGO_CALLBACK_URL"
              value = "https://nango.evertrue.com/oauth/callback"
            },
            {
              name  = "TEMPORAL_ADDRESS"
              value = "evertrue-flows.guqns.tmprl.cloud:7233"
            },
            {
              name  = "TEMPORAL_NAMESPACE"
              value = "evertrue-flows.guqns"
            },
            {
              name  = "NODE_ENV"
              value = "production"
            },
            {
              name  = "NANGO_ENTERPRISE"
              value = "true"
            },
            {
              name  = "RUNNER_TYPE"
              value = "REMOTE"
            },
            {
              name  = "MAILGUN_API_KEY"
              value = "**************************************************"
            },
            {
              name  = "PERSIST_SERVICE_URL"
              value = "http://${module.alb["nango-persist"].lb_dns_name}"
            },
            {
              name  = "JOBS_SERVICE_URL"
              value = "http://${module.alb["nango-jobs"].lb_dns_name}"
            },
            {
              name  = "RUNNER_SERVICE_URL"
              value = "http://${module.alb["nango-runner"].lb_dns_name}"
            },
            {
              name  = "NANGO_RUNNER_PATH"
              value = "/nango/packages/runner/dist/app.js"
            },
            {
              name  = "NANGO_ADMIN_UUID"
              value = "25bf0b16-54a3-49e6-aec0-fd4b514c0b2e"
            },
            {
              name  = "ORCHESTRATOR_SERVICE_URL"
              value = "http://${module.alb["nango-orchestrator"].lb_dns_name}"
            },
            {
              name  = "NANGO_LOGS_ENABLED"
              value = "true"
            },
            {
              name  = "NANGO_LOGS_ES_PWD"
              value = "lyJbF5Q8fzCd2xiiRb4FMdOz"
            },
            {
              name  = "NANGO_LOGS_ES_URL"
              value = "https://nango-evertrue.es.us-west-2.aws.found.io/"
            },
            {
              name  = "NANGO_LOGS_ES_USER"
              value = "elastic"
            },
  # Datadog environment variables for metrics ####################
            {
              name  = "DD_AGENT_HOST"
              value = "datadog-agent.nango-services"
            },
            {
              name  = "DD_DOGSTATSD_PORT"
              value = "8125"
            },
            {
              name  = "DD_ENV"
              value = "production"
            },
            {
              name  = "DD_SERVICE"
              value = "nango-jobs"
            },
            {
              name  = "DD_VERSION"
              value = "v1.0.0"
            },
            {
              name  = "DD_PROFILING_ENABLED"
              value = "true"
            },
            {
              name  = "DD_DOGSTATSD_NON_LOCAL_TRAFFIC"
              value = "true"
            },
            {
              name  = "DD_METRICS_ENABLED"
              value = "true"
            },
            {
              name = "DD_SITE",
              value = "datadoghq.com"
            },
            {
              name = "NANGO_LOGS_ES_PREFIX"
              value = "prod"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_OPERATIONS"
              value = "prod-operations"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_MESSAGES"
              value = "prod-messages"
            }
          ]
          readonlyRootFilesystem = false
          mountPoints = [
            {
              sourceVolume  = "secrets",
              containerPath = "/etc/secrets/",
              readOnly      = false
            }
          ]
        }
      }
      cpu    = 1024
      memory = 2048
      runtime_platform = {
        cpu_architecture        = "X86_64"
        operating_system_family = "LINUX"
      }
      network_mode               = "awsvpc"
      create_tasks_iam_role      = true
      tasks_iam_role_name        = "nango-jobs-prod-task"
      tasks_iam_role_description = "These permissions are assumed by the containers running in the task"
      tasks_iam_role_policies = {
        AmazonECSTaskExecutionRolePolicy = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
        S3ProdBucketAccess               = "${module.iam_policy_from_data_source["ProdS3Access"].arn}"
      }
      create_task_exec_iam_role      = true
      task_exec_iam_role_name        = "nango-jobs-prod-task-exec"
      task_exec_iam_role_description = "These permissions are used by ECS to run the task"
      task_exec_iam_statements = [
        {
          sid    = "GetSecrets"
          effect = "Allow"
          actions = [
            "secretsmanager:GetSecretValue"
          ]
          resources = ["arn:aws:secretsmanager:*:*:secret:*"]
        }
      ]

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "ECS Cluster Nango Jobs Service", CreateDate = "20240214", })}"
    }
    nango-persist = {
      # Service Definition
      scheduling_strategy = "REPLICA"
      deployment_circuit_breaker = {
        enable   = true
        rollback = true
      }
      deployment_controller = {
        type = "ECS"
      }
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      desired_count                      = 4
      health_check_grace_period_seconds  = 0
      iam_role_arn                       = aws_iam_service_linked_role.this["ecs.amazonaws.com"].arn
      load_balancer = {
        service = {
          target_group_arn = module.alb["nango-persist"].target_group_arns[0]
          container_name   = "nango-persist"
          container_port   = 3007
        }
      }
      enable_autoscaling       = true
      autoscaling_min_capacity = 4
      autoscaling_max_capacity = 4
      autoscaling_policies = {
        cpu = {
          policy_type = "TargetTrackingScaling"

          target_tracking_scaling_policy_configuration = {
            predefined_metric_specification = {
              predefined_metric_type = "ECSServiceAverageCPUUtilization"
            }
            scale_in_cooldown  = 60
            scale_out_cooldown = 300
            target_value       = 70
          }
        }
      }
      security_group_ids = [module.security_group["prod-self"].security_group_id]
      # Task Definition
      requires_compatibilities = ["FARGATE"]
      family                   = "nango-persist"
      enable_execute_command   = true   #added to enable exec into container
      container_definitions = {
        nango-persist = {
          image   = "docker.io/nangohq/nango:0e82ec7811ffb939367a543e386353d06bde499a"
          command = ["node", "packages/persist/dist/app.js"]
          portMappings = [
            {
              name          = "nango-persist-3007-tcp"
              containerPort = 3007
              protocol      = "tcp"
              appProtocol   = "http"
              hostPort      = 3007
            }
          ]
          logConfiguration = {
            logDriver = "awslogs",
            options = {
              awslogs-group         = module.log_group["nango-persist"].cloudwatch_log_group_name
              awslogs-region        = "us-east-1"
              awslogs-stream-prefix = "ecs"
            }
          }
          essential = true
          secrets = [
            {
              name      = "NANGO_DB_PASSWORD"
              valueFrom = "${module.secrets-manager["nango_db_user_password"].secret_arns.nango_db_user_password}:nango_db_user_password::"
            },
            {
              name      = "RECORDS_DATABASE_URL"
              valueFrom = "${module.secrets-manager["nango_db_user_password"].secret_arns.nango_db_user_password}:records_database_url::"
            },
            {
              name      = "NANGO_ENCRYPTION_KEY"
              valueFrom = "${module.secrets-manager["nango_secrets"].secret_arns.nango_secrets}:nango_encryption_key::"
            },
            {
              name      = "DD_API_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:datadog-api-key-kGP4f7:api_key::"
            },
            {
              name      = "DD_APP_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:data-dog-app-key-949kBz:data-dog-app-key::"
            }
          ]
          environment = [
            {
              name  = "AWS_REGION",
              value = data.aws_region.current.name
            },
            {
              name  = "AWS_BUCKET_NAME",
              value = module.s3_bucket["et-nango-prod"].s3_bucket_id
            },
            {
              name  = "NANGO_DB_HOST",
              value = module.aurora_cluster["prod"].cluster_endpoint
            },
            {
              name  = "NANGO_DB_NAME",
              value = "nango"
            },
            {
              name  = "NANGO_DB_USER"
              value = "nango"
            },
            {
              name  = "NANGO_DB_PORT"
              value = 5432
            },
            {
              name  = "NANGO_DB_SSL"
              value = false
            },
            {
              name  = "NANGO_ENTERPRISE"
              value = "true"
            },
            {
              name  = "NODE_ENV"
              value = "production"
            },
            {
              name  = "NANGO_CALLBACK_URL"
              value = "https://nango.evertrue.com/oauth/callback"
            },
            {
              name  = "NANGO_SERVER_URL"
              value = "https://nango.evertrue.com"
            },
            {
              name  = "NANGO_ADMIN_UUID"
              value = "25bf0b16-54a3-49e6-aec0-fd4b514c0b2e"
            },
            {
              name  = "ORCHESTRATOR_SERVICE_URL"
              value = "http://${module.alb["nango-orchestrator"].lb_dns_name}"
            },
            {
              name  = "NANGO_LOGS_ENABLED"
              value = "true"
            },
            {
              name  = "NANGO_LOGS_ES_PWD"
              value = "lyJbF5Q8fzCd2xiiRb4FMdOz"
            },
            {
              name  = "NANGO_LOGS_ES_URL"
              value = "https://nango-evertrue.es.us-west-2.aws.found.io/"
            },
            {
              name  = "NANGO_LOGS_ES_USER"
              value = "elastic"
            },
  # Datadog environment variables for metrics ####################
            {
              name  = "DD_AGENT_HOST"
              value = "datadog-agent.nango-services"
            },
            {
              name  = "DD_DOGSTATSD_PORT"
              value = "8125"
            },
            {
              name  = "DD_ENV"
              value = "production"
            },
            {
              name  = "DD_SERVICE"
              value = "nango-persist"
            },
            {
              name  = "DD_VERSION"
              value = "v1.0.0"
            },
            {
              name  = "DD_PROFILING_ENABLED"
              value = "true"
            },
            {
              name  = "DD_DOGSTATSD_NON_LOCAL_TRAFFIC"
              value = "true"
            },
            {
              name  = "DD_METRICS_ENABLED"
              value = "true"
            },
            {
              name = "DD_SITE",
              value = "datadoghq.com"
            },
            {
              name = "NANGO_LOGS_ES_PREFIX"
              value = "prod"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_OPERATIONS"
              value = "prod-operations"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_MESSAGES"
              value = "prod-messages"
            }
          ]
          readonlyRootFilesystem = false
        }
      }
      cpu    = 512
      memory = 1024
      runtime_platform = {
        cpu_architecture        = "X86_64"
        operating_system_family = "LINUX"
      }
      network_mode               = "awsvpc"
      create_tasks_iam_role      = true
      tasks_iam_role_name        = "nango-persist-prod-task"
      tasks_iam_role_description = "These permissions are assumed by the containers running in the task"
      tasks_iam_role_policies = {
        AmazonECSTaskExecutionRolePolicy = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
      }
      create_task_exec_iam_role      = true
      task_exec_iam_role_name        = "nango-persist-prod-task-exec"
      task_exec_iam_role_description = "These permissions are used by ECS to run the task"
      task_exec_iam_statements = [
        {
          sid    = "GetSecrets"
          effect = "Allow"
          actions = [
            "secretsmanager:GetSecretValue"
          ]
          resources = ["arn:aws:secretsmanager:*:*:secret:*"]
        }
      ]

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "ECS Cluster Nango Persist Service", CreateDate = "20240214", })}"
    }
    nango-runner = {
      # Service Definition
      scheduling_strategy = "REPLICA"
      deployment_circuit_breaker = {
        enable   = true
        rollback = true
      }
      deployment_controller = {
        type = "ECS"
      }
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      desired_count                      = 1
      health_check_grace_period_seconds  = 0
      iam_role_arn                       = aws_iam_service_linked_role.this["ecs.amazonaws.com"].arn
      load_balancer = {
        service = {
          target_group_arn = module.alb["nango-runner"].target_group_arns[0]
          container_name   = "nango-runner"
          container_port   = 80
        }
      }
      enable_autoscaling       = false
      autoscaling_min_capacity = 1
      autoscaling_max_capacity = 1
      autoscaling_policies = {
        cpu = {
          policy_type = "TargetTrackingScaling"

          target_tracking_scaling_policy_configuration = {
            predefined_metric_specification = {
              predefined_metric_type = "ECSServiceAverageCPUUtilization"
            }
            scale_in_cooldown  = 60
            scale_out_cooldown = 300
            target_value       = 70
          }
        }
      }
      security_group_ids = [module.security_group["prod-self"].security_group_id]
      # Task Definition
      requires_compatibilities = ["FARGATE"]
      family                   = "nango-runner"
      enable_execute_command   = true   #added to enable exec into container
      container_definitions = {
        nango-runner = {
          image = "docker.io/nangohq/nango:0e82ec7811ffb939367a543e386353d06bde499a"
          command = ["node", "packages/runner/dist/app.js", "80", "dockerized-runner"]
          portMappings = [
            {
              name          = "nango-runner-80-tcp"
              containerPort = 80
              protocol      = "tcp"
              appProtocol   = "http"
              hostPort      = 80
            }
          ]
          logConfiguration = {
            logDriver = "awslogs",
            options = {
              awslogs-group         = module.log_group["nango-runner"].cloudwatch_log_group_name
              awslogs-region        = "us-east-1"
              awslogs-stream-prefix = "ecs"
            }
          }
          essential = true
          secrets = [
            {
              name      = "DD_API_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:datadog-api-key-kGP4f7:api_key::"
            },
            {
              name      = "DD_APP_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:data-dog-app-key-949kBz:data-dog-app-key::"
            }
          ]

          environment = [
            {
              name  = "PERSIST_SERVICE_URL"
              value = "http://${module.alb["nango-persist"].lb_dns_name}"
            },
            {
              name  = "JOBS_SERVICE_URL"
              value = "http://${module.alb["nango-jobs"].lb_dns_name}"
            },
            {
              name  = "NANGO_ENTERPRISE"
              value = "true"
            },
            {
              name  = "NANGO_ADMIN_UUID"
              value = "25bf0b16-54a3-49e6-aec0-fd4b514c0b2e"
            },
            {
              name  = "NANGO_LOGS_ENABLED"
              value = "true"
            },
            {
              name  = "NANGO_LOGS_ES_PWD"
              value = "lyJbF5Q8fzCd2xiiRb4FMdOz"
            },
            {
              name  = "NANGO_LOGS_ES_URL"
              value = "https://nango-evertrue.es.us-west-2.aws.found.io/"
            },
            {
              name  = "NANGO_LOGS_ES_USER"
              value = "elastic"
            },
            {
              name  = "ORCHESTRATOR_SERVICE_URL"
              value = "http://${module.alb["nango-orchestrator"].lb_dns_name}"
            },
            {
              name  = "RUNNER_NODE_ID"
              value = "1"  #This should be temporary as the image above does not have it by default nango team states they will make it default in later release
            },
  # Datadog environment variables for metrics ####################
            {
              name  = "DD_AGENT_HOST"
              value = "datadog-agent.nango-services"
            },
            {
              name  = "DD_DOGSTATSD_PORT"
              value = "8125"
            },
            {
              name  = "DD_ENV"
              value = "production"
            },
            {
              name  = "DD_SERVICE"
              value = "nango-runner"
            },
            {
              name  = "DD_VERSION"
              value = "v1.0.0"
            },
            {
              name  = "DD_PROFILING_ENABLED"
              value = "true"
            },
            {
              name  = "DD_DOGSTATSD_NON_LOCAL_TRAFFIC"
              value = "true"
            },
            {
              name  = "DD_METRICS_ENABLED"
              value = "true"
            },
            {
              name = "DD_SITE",
              value = "datadoghq.com"
            },
            {
              name = "NANGO_LOGS_ES_PREFIX"
              value = "prod"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_OPERATIONS"
              value = "prod-operations"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_MESSAGES"
              value = "prod-messages"
            }

          ]
          readonlyRootFilesystem = false
        }
      }
      cpu    = 2048
      memory = 4096
      runtime_platform = {
        cpu_architecture        = "X86_64"
        operating_system_family = "LINUX"
      }
      network_mode               = "awsvpc"
      create_tasks_iam_role      = true
      tasks_iam_role_name        = "nango-runner-prod-task"
      tasks_iam_role_description = "These permissions are assumed by the containers running in the task"
      tasks_iam_role_policies = {
        AmazonECSTaskExecutionRolePolicy = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
      }
      create_task_exec_iam_role      = true
      task_exec_iam_role_name        = "nango-runner-prod-task-exec"
      task_exec_iam_role_description = "These permissions are used by ECS to run the task"
      task_exec_iam_statements = [
        {
          sid    = "GetSecrets"
          effect = "Allow"
          actions = [
            "secretsmanager:GetSecretValue"
          ]
          resources = ["arn:aws:secretsmanager:*:*:secret:*"]
        }
      ]

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "ECS Cluster Nango Runner Service", CreateDate = "20240214", })}"
    }
    nango-orchestrator = {
      # Service Definition
      scheduling_strategy = "REPLICA"
      deployment_circuit_breaker = {
        enable   = true
        rollback = true
      }
      deployment_controller = {
        type = "ECS"
      }
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      desired_count                      = 1
      health_check_grace_period_seconds  = 0
      iam_role_arn                       = aws_iam_service_linked_role.this["ecs.amazonaws.com"].arn
      load_balancer = {
        service = {
          target_group_arn = module.alb["nango-orchestrator"].target_group_arns[0]
          container_name   = "nango-orchestrator"
          container_port   = 3008
        }
      }
      enable_autoscaling       = true
      autoscaling_min_capacity = 1
      autoscaling_max_capacity = 1
      autoscaling_policies = {
        cpu = {
          policy_type = "TargetTrackingScaling"

          target_tracking_scaling_policy_configuration = {
            predefined_metric_specification = {
              predefined_metric_type = "ECSServiceAverageCPUUtilization"
            }
            scale_in_cooldown  = 60
            scale_out_cooldown = 300
            target_value       = 70
          }
        }
      }
      security_group_ids = [module.security_group["prod-self"].security_group_id]
      # Task Definition
      requires_compatibilities = ["FARGATE"]
      family                   = "nango-orchestrator"
      enable_execute_command   = true   #added to enable exec into container
      container_definitions = {
        nango-orchestrator = {
          image = "docker.io/nangohq/nango:0e82ec7811ffb939367a543e386353d06bde499a"
          command = ["node", "packages/orchestrator/dist/app.js"]
          portMappings = [
            {
              name          = "nango-orchestrator-3008-tcp"
              containerPort = 3008
              protocol      = "tcp"
              appProtocol   = "http"
              hostPort      = 3008
            }
          ]
          logConfiguration = {
            logDriver = "awslogs",
            options = {
              awslogs-group         = module.log_group["nango-orchestrator"].cloudwatch_log_group_name
              awslogs-region        = "us-east-1"
              awslogs-stream-prefix = "ecs"
            }
          }
          essential = true
          secrets = [
            {
              name      = "NANGO_DB_PASSWORD"
              valueFrom = "${module.secrets-manager["nango_db_user_password"].secret_arns.nango_db_user_password}:nango_db_user_password::"
            },
            {
              name      = "RECORDS_DATABASE_URL"
              valueFrom = "${module.secrets-manager["nango_db_user_password"].secret_arns.nango_db_user_password}:records_database_url::"
            },
            {
              name      = "NANGO_ENCRYPTION_KEY"
              valueFrom = "${module.secrets-manager["nango_secrets"].secret_arns.nango_secrets}:nango_encryption_key::"
            },
            {
              name      = "DD_API_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:datadog-api-key-kGP4f7:api_key::"
            },
            {
              name      = "DD_APP_KEY"
              valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:data-dog-app-key-949kBz:data-dog-app-key::"
            }

          ]
          environment = [
            {
              name  = "AWS_REGION",
              value = data.aws_region.current.name
            },
            {
              name  = "AWS_BUCKET_NAME",
              value = module.s3_bucket["et-nango-prod"].s3_bucket_id
            },
            {
              name  = "NANGO_DB_HOST",
              value = module.aurora_cluster["prod"].cluster_endpoint
            },
            {
              name  = "NANGO_DB_NAME",
              value = "nango"
            },
            {
              name  = "NANGO_DB_USER"
              value = "nango"
            },
            {
              name  = "NANGO_DB_PORT"
              value = 5432
            },
            {
              name  = "NANGO_DB_SSL"
              value = false
            },
            {
              name  = "NANGO_SERVER_URL"
              value = "https://nango.evertrue.com"
            },
            {
              name  = "NANGO_CALLBACK_URL"
              value = "https://nango.evertrue.com/oauth/callback"
            },
            {
              name  = "TEMPORAL_ADDRESS"
              value = "evertrue-flows.guqns.tmprl.cloud:7233"
            },
            {
              name  = "TEMPORAL_NAMESPACE"
              value = "evertrue-flows.guqns"
            },
            {
              name  = "NODE_ENV"
              value = "production"
            },
            {
              name  = "NANGO_ENTERPRISE"
              value = "true"
            },
            {
              name  = "MAILGUN_API_KEY"
              value = "**************************************************"
            },
            {
              name  = "PERSIST_SERVICE_URL"
              value = "http://${module.alb["nango-persist"].lb_dns_name}"
            },
            {
              name  = "JOBS_SERVICE_URL"
              value = "http://${module.alb["nango-jobs"].lb_dns_name}"
            },
            {
              name  = "RUNNER_SERVICE_URL"
              value = "http://${module.alb["nango-runner"].lb_dns_name}"
            },
            {
              name  = "NANGO_LOGS_ENABLED"
              value = "true"
            },
            {
              name  = "NANGO_LOGS_ES_PWD"
              value = "lyJbF5Q8fzCd2xiiRb4FMdOz"
            },
            {
              name  = "NANGO_LOGS_ES_URL"
              value = "https://nango-evertrue.es.us-west-2.aws.found.io/"
            },
            {
              name  = "NANGO_LOGS_ES_USER"
              value = "elastic"
            },
            {
              name  = "NANGO_ADMIN_UUID"
              value = "25bf0b16-54a3-49e6-aec0-fd4b514c0b2e"
            },
  # Datadog environment variables for metrics ####################
            {
              name  = "DD_AGENT_HOST"
              value = "datadog-agent.nango-services"
            },
            {
              name  = "DD_DOGSTATSD_PORT"
              value = "8125"
            },
            {
              name  = "DD_ENV"
              value = "production"
            },
            {
              name  = "DD_SERVICE"
              value = "nango-orchestrator"
            },
            {
              name  = "DD_VERSION"
              value = "v1.0.0"
            },
            {
              name  = "DD_PROFILING_ENABLED"
              value = "true"
            },
            {
              name  = "DD_DOGSTATSD_NON_LOCAL_TRAFFIC"
              value = "true"
            },
            {
              name  = "DD_METRICS_ENABLED"
              value = "true"
            },
            {
              name = "DD_SITE",
              value = "datadoghq.com"
            },
            {
              name = "NANGO_LOGS_ES_PREFIX"
              value = "prod"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_OPERATIONS"
              value = "prod-operations"
            },
            {
              name = "NANGO_LOGS_ES_INDEX_MESSAGES"
              value = "prod-messages"
            }

          ]
          readonlyRootFilesystem = false
        }
      }
      cpu    = 512
      memory = 1024
      runtime_platform = {
        cpu_architecture        = "X86_64"
        operating_system_family = "LINUX"
      }
      network_mode               = "awsvpc"
      create_tasks_iam_role      = true
      tasks_iam_role_name        = "nango-orchestrator-prod-task"
      tasks_iam_role_description = "These permissions are assumed by the containers running in the task"
      tasks_iam_role_policies = {
        AmazonECSTaskExecutionRolePolicy = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
      }
      create_task_exec_iam_role      = true
      task_exec_iam_role_name        = "nango-orchestrator-prod-task-exec"
      task_exec_iam_role_description = "These permissions are used by ECS to run the task"
      task_exec_iam_statements = [
        {
          sid    = "GetSecrets"
          effect = "Allow"
          actions = [
            "secretsmanager:GetSecretValue"
          ]
          resources = ["arn:aws:secretsmanager:*:*:secret:*"]
        }
      ]

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "ECS Cluster Nango Orchestrator Service", CreateDate = "20240214", })}"
    }
  }
}

module "ecs_service" {
  source = "terraform-aws-modules/ecs/aws//modules/service"

  for_each = local.ecs_service

  name        = each.key
  cluster_arn = module.ecs_cluster["nango-prod"].arn
  # Service Definition
  launch_type                        = try(each.value.launch_type, "FARGATE")
  scheduling_strategy                = try(each.value.scheduling_strategy, "REPLICA")
  alarms                             = try(each.value.alarms, null)
  capacity_provider_strategy         = try(each.value.capacity_provider_strategy, {})
  deployment_circuit_breaker         = try(each.value.deployment_circuit_breaker, {})
  deployment_controller              = try(each.value.deployment_controller, {})
  deployment_maximum_percent         = try(each.value.deployment_maximum_percent, 200)
  deployment_minimum_healthy_percent = try(each.value.deployment_minimum_healthy_percent, 66)
  desired_count                      = try(each.value.desired_count, 1)
  enable_ecs_managed_tags            = try(each.value.enable_ecs_managed_tags, true)
  enable_execute_command             = try(each.value.enable_execute_command, false)
  force_new_deployment               = try(each.value.force_new_deployment, true)
  health_check_grace_period_seconds  = try(each.value.health_check_grace_period_seconds, null)
  create_iam_role                    = true
  iam_role_arn                       = try(each.value.iam_role_arn, null)
  load_balancer                      = try(each.value.load_balancer, {})
  subnet_ids                         = module.vpc["prod"].private_subnets
  security_group_ids                 = try(each.value.security_group_ids, [])
  ordered_placement_strategy         = try(each.value.ordered_placement_strategy, {})
  placement_constraints              = try(each.value.placement_constraints, {})
  service_connect_configuration      = try(each.value.service_connect_configuration, {})
  service_registries                 = try(each.value.service_registries, null)
  triggers                           = try(each.value.triggers, {})
  enable_autoscaling                 = try(each.value.enable_autoscaling, true)
  autoscaling_min_capacity           = try(each.value.autoscaling_min_capacity, 1)
  autoscaling_max_capacity           = try(each.value.autoscaling_max_capacity, 10)
  autoscaling_policies               = try(each.value.autoscaling_policies, {})
  autoscaling_scheduled_actions      = try(each.value.autoscaling_scheduled_actions, {})
  propagate_tags                     = try(each.value.propagate_tags, null)
  # Task Definition
  requires_compatibilities              = try(each.value.requires_compatibilities, ["EC2"])
  family                                = try(each.value.family, null)
  container_definitions                 = try(each.value.container_definitions, {})
  cpu                                   = try(each.value.cpu, 1024)
  memory                                = try(each.value.memory, 2048)
  ephemeral_storage                     = try(each.value.ephemeral_storage, null)
  volume                                = try(each.value.volume, {})
  runtime_platform                      = try(each.value.runtime_platform, {})
  task_exec_iam_role_arn                = try(each.value.task_exec_iam_role_arn, null)
  create_task_exec_iam_role             = try(each.value.create_task_exec_iam_role, false)
  task_exec_iam_role_name               = try(each.value.task_exec_iam_role_name, null)
  task_exec_iam_role_description        = try(each.value.task_exec_iam_role_description, null)
  task_exec_iam_role_policies           = try(each.value.task_exec_iam_role_policies, {})
  task_exec_iam_statements              = try(each.value.task_exec_iam_statements, [])
  tasks_iam_role_arn                    = try(each.value.tasks_iam_role_arn, null)
  create_tasks_iam_role                 = try(each.value.create_tasks_iam_role, false)
  tasks_iam_role_name                   = try(each.value.tasks_iam_role_name, null)
  tasks_iam_role_description            = try(each.value.tasks_iam_role_description, null)
  tasks_iam_role_policies               = try(each.value.tasks_iam_role_policies, {})
  tasks_iam_role_statements             = try(each.value.tasks_iam_role_statements, [])
  # inference_accelerator argument removed in newer ECS module versions
  # inference_accelerator                 = try(each.value.inference_accelerator, {})
  ipc_mode                              = try(each.value.ipc_mode, null)
  pid_mode                              = try(each.value.pid_mode, null)
  network_mode                          = try(each.value.network_mode, "awsvpc")
  proxy_configuration                   = try(each.value.proxy_configuration, null)
  task_definition_placement_constraints = try(each.value.task_definition_placement_constraints, {})
  skip_destroy                          = try(each.value.skip_destroy, null)
  task_tags                             = try(each.value.task_tags, {})
  # Both
  tags = try(each.value.tags, {})
}
