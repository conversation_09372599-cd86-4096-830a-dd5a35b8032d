locals {
  instances = {
  }
}


module "ec2instance" {
  source = "terraform-aws-modules/ec2-instance/aws"

  for_each = local.instances

  name                   = each.key
  ami                    = try(each.value.ami, "")
  instance_type          = try(each.value.instance_type, "")
  key_name               = try(each.value.key_name, "")
  iam_instance_profile   = try(each.value.iam_instance_profile, "")
  monitoring             = true
  vpc_security_group_ids = try(each.value.vpc_security_group_ids, [])
  subnet_id              = try(each.value.subnet_id, "")
  private_ip             = try(each.value.private_ip, null)
  # ebs_block_device argument removed in newer versions - use block_device_mappings instead
  # ebs_block_device       = try(each.value.ebs_block_device, [])
  tags                   = try(each.value.tags, {})
}
