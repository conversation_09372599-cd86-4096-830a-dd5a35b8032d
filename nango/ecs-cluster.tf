locals {
  ecs_cluster = {
    nango-prod = {
      # Capacity provider - autoscaling groups
      default_capacity_provider_use_fargate = true
      fargate_capacity_providers = {
        FARGATE = {
          default_capacity_provider_strategy = {
            weight = 50
            base   = 20
          }
        }
      }
      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22832", Creator = "<EMAIL>", Tier = "Prod", Component = "ECS Cluster", CreateDate = "20240213" })}"
    }
  }
}


module "ecs_cluster" {
  source  = "terraform-aws-modules/ecs/aws//modules/cluster"
  version = "~> 5.7"

  for_each = local.ecs_cluster

  cluster_name                          = each.key
  default_capacity_provider_use_fargate = try(each.value.default_capacity_provider_use_fargate, false)
  autoscaling_capacity_providers        = try(each.value.autoscaling_capacity_providers, {})
  fargate_capacity_providers            = try(each.value.fargate_capacity_providers, {})
  tags                                  = try(each.value.tags, {})
}
