locals {
  secrets = {
    nango_evertrue_com_acme_reg_private_key = {
      secrets = {
        nango_evertrue_com_acme_reg_private_key = {
          description             = "Private key for nango.evertrue.com ACME (Let's Encrypt) registration"
          recovery_window_in_days = 7
          secret_string           = "Fake initial value"

        }
      }
      tags = {
        Owner       = "DevOps team"
        Environment = "Dev"
        Terraform   = true
      }
    }
    nango_evertrue_com_cert_private_key = {
      secrets = {
        nango_evertrue_com_cert_private_key = {
          description             = "Private key for nango.evertrue.com SSL certificate"
          recovery_window_in_days = 7
          secret_string           = "Fake initial value"

        }
      }
      tags = {
        Owner       = "DevOps team"
        Environment = "Dev"
        Terraform   = true
      }
    }
    nango_db_user_password = {
      secrets = {
        nango_db_user_password = {
          description             = "Postgress password for the nango user"
          recovery_window_in_days = 7
          secret_string           = "Fake initial value"

        }
      }
      tags = {
        Owner       = "DevOps team"
        Environment = "Prod"
        Terraform   = true
      }
    }
    nango_secrets = {
      secrets = {
        nango_secrets = {
          description             = "Nango Secrets"
          recovery_window_in_days = 7
          secret_string           = "Fake initial value"

        }
      }
      tags = {
        Owner       = "DevOps team"
        Environment = "Prod"
        Terraform   = true
      }
    }
  }
}

module "secrets-manager" {
  source  = "lgallard/secrets-manager/aws"
  version = "~> 0.8"

  for_each = local.secrets

  secrets   = try(each.value.secrets, {})
  unmanaged = true
  tags      = try(each.value.tags, {})
}


#===================

resource "aws_acm_certificate" "nango_cert" {
  domain_name       = "nango.evertrue.com"
  validation_method = "DNS"

  tags = {
    Environment = "Dev"
    Terraform   = true
    Owner       = "DevOps team"
  }

  lifecycle {
    create_before_destroy = true
  }
}
