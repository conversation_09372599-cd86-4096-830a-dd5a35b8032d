################################################################################
################################################################################
# Let's Encrypt registration
################################################################################
################################################################################
resource "acme_registration" "nango_evertrue_com_reg" {
  account_key_pem = jsondecode(data.aws_secretsmanager_secret_version.nango_evertrue_com_acme_reg_private_key_current.secret_string)["reg_key"]
  email_address   = "<EMAIL>"
}

################################################################################
# *.pledgemine.com
################################################################################
resource "tls_cert_request" "nango_evertrue_com" {
  private_key_pem = jsondecode(data.aws_secretsmanager_secret_version.nango_evertrue_com_cert_private_key_current.secret_string)["cert_key"]
  dns_names       = ["nango.evertrue.com"]

  subject {
    common_name         = "nango.evertrue.com"
    organizational_unit = "DevOps"
    organization        = "Evertrue"
    locality            = "Boston"
    province            = "Massachusetts"
    country             = "US"




  }
}

# Signed certificate from Let's Encrypt
resource "acme_certificate" "nango_evertrue_com_certificate" {
  account_key_pem         = acme_registration.nango_evertrue_com_reg.account_key_pem
  certificate_request_pem = tls_cert_request.nango_evertrue_com.cert_request_pem

  dns_challenge {
    provider = "route53"

    config = {
      AWS_PROFILE        = "evertrue-sso"
      AWS_HOSTED_ZONE_ID = "Z20FI4RWVXAMXL"
      AWS_TTL            = "60"
    }
  }
}

# Let's Encrypt manually created certificate
resource "aws_acm_certificate" "nango_evertrue_com" {
  private_key       = jsondecode(data.aws_secretsmanager_secret_version.nango_evertrue_com_cert_private_key_current.secret_string)["cert_key"]
  certificate_body  = acme_certificate.nango_evertrue_com_certificate.certificate_pem
  certificate_chain = acme_certificate.nango_evertrue_com_certificate.issuer_pem
}
