
# CloudWatch Log Group for Datadog Agent
resource "aws_cloudwatch_log_group" "datadog_agent" {
  name              = "/ecs/datadog-agent-task"
  retention_in_days = 14
}

# Security Group for Datadog Agent
resource "aws_security_group" "datadog_sg" {
  name        = "datadog-agent-sg"
  description = "Security group for Datadog agent to collect logs and send to Datadog"
  vpc_id      = module.vpc["prod"].vpc_id

  ingress {
    description     = "Allow inbound traffic from ECS services security group"
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    security_groups = ["sg-0794dce861827a9af"]
  }

  ingress {
    description = "Allow inbound traffic for DogStatsD metrics collection"
    from_port   = 8125
    to_port     = 8125
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
  description = "Allow inbound traffic for OpenTelemetry HTTP"
  from_port   = 4318
  to_port     = 4318
  protocol    = "tcp"
  cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    description = "Allow all outbound traffic to send logs to Datadog"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "datadog-agent-sg"
  }
}

# ECS Task Definition for Datadog Agent

resource "aws_ecs_task_definition" "datadog_agent" {
  family                   = "datadog-agent-task"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = "256"
  memory                   = "512"
  execution_role_arn       = aws_iam_role.datadog_agent_role.arn
  task_role_arn            = aws_iam_role.datadog_agent_role.arn


  container_definitions = jsonencode([{
    name      = "datadog-agent"
    image     = "datadog/agent:latest"
    cpu       = 10
    memory    = 256
    essential = true
    #enableExecuteCommand = true
        secrets = [
      {
        name      = "DD_API_KEY"
        valueFrom = "arn:aws:secretsmanager:us-east-1:905418190808:secret:datadog-api-key-kGP4f7:api_key::"
      }
    ]
    portMappings = [
      {
        containerPort = 8126
        hostPort      = 8126
      },
      {
        containerPort = 8125
        hostPort      = 8125
        protocol      = "udp"
      },
      {
        containerPort = 4318
        hostPort      = 4318
        protocol      = "tcp"
      }

    ]
    environment = [
      {
        name  = "DD_APM_ENABLED"
        value = "false"
      },
      {
        name  = "DD_PROFILING_ENABLED"
        value = "true"
      },
      {
        name  = "DD_DOGSTATSD_NON_LOCAL_TRAFFIC"
        value = "true"
      },
      {
        name  = "ECS_FARGATE"
        value = "true"
      },
      {
        name  = "DD_LOGS_ENABLED"
        value = "true"
      },
      {
        name  = "DD_LOGS_CONFIG_CONTAINER_COLLECT_ALL"
        value = "true"
      }, 
      {
        name  = "DD_USE_DOGSTATSD"
        value = "true"
      },
      {
        name  = "DD_LOG_LEVEL"
        value = "debug"
      },
      {
        name  = "DD_OTLP_CONFIG_RECEIVER_PROTOCOLS_HTTP_ENDPOINT"
        value = "0.0.0.0:4318"
      },
      {
        name  = "DD_DOGSTATSD_METRICS_STATS_ENABLE"
        value = "true"
      }

    ]
    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-group         = aws_cloudwatch_log_group.datadog_agent.name
        awslogs-region        = data.aws_region.current.name
        awslogs-stream-prefix = "ecs"
      }
    }
  }])
}

# ECS Service for Datadog Agent
resource "aws_ecs_service" "datadog_agent_service" {
  name            = "datadog-agent-service"
  cluster         = var.cluster_name
  task_definition = aws_ecs_task_definition.datadog_agent.arn
  desired_count   = 1
  launch_type     = "FARGATE"
  enable_execute_command = true

  network_configuration {
    subnets         = module.vpc["prod"].private_subnets
    security_groups = [aws_security_group.datadog_sg.id]
  }

  service_registries {
    registry_arn = aws_service_discovery_service.datadog_agent.arn
  }
}

resource "aws_service_discovery_service" "datadog_agent" {
  name = "datadog-agent"

  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.nango_services.id

    dns_records {
      ttl  = 60
      type = "A"
    }
  }

  health_check_custom_config {
    failure_threshold = 1
  }
}

resource "aws_service_discovery_private_dns_namespace" "nango_services" {
  name        = "nango-services"
  description = "Private DNS namespace for Nango services"
  vpc         = module.vpc["prod"].vpc_id
}

# IAM Role for Datadog Agent
resource "aws_iam_role" "datadog_agent_role" {
  name = "datadog-agent-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

# IAM Policy for Datadog Agent
# IAM Policy for Datadog Agent
resource "aws_iam_policy" "datadog_agent_policy" {
  name        = "datadog-agent-policy"
  description = "Policy to allow ECS Fargate metrics collection and CloudWatch Logs access"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ecs:ListClusters",
          "ecs:ListContainerInstances",
          "ecs:DescribeContainerInstances",
          "ecs:DescribeTasks",
          "ecs:DescribeTaskDefinition"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "secretsmanager:GetSecretValue"
        ],
        Resource = [
          "arn:aws:secretsmanager:*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = "${aws_cloudwatch_log_group.datadog_agent.arn}:*"
      },
      {
        Effect = "Allow",
        Action = [
          "ssmmessages:CreateControlChannel",
          "ssmmessages:CreateDataChannel",
          "ssmmessages:OpenControlChannel",
          "ssmmessages:OpenDataChannel"
        ],
        Resource = "*"
      }
    ]
  })
}

# Attach the IAM Policy to the IAM Role
resource "aws_iam_role_policy_attachment" "datadog_agent_role_attach" {
  role       = aws_iam_role.datadog_agent_role.name
  policy_arn = aws_iam_policy.datadog_agent_policy.arn
}