locals {
  keypairs = {
    nango_prod_cloud_engineering = {
    }
  }
}

module "key_pair" {
  source  = "terraform-aws-modules/key-pair/aws"
  version = "2.0.2"

  for_each = local.keypairs

  key_name           = each.key
  create_private_key = true
}

# EC2
resource "aws_ec2_serial_console_access" "us-east-1" {
  enabled = true
}

resource "aws_ebs_encryption_by_default" "us-east-1" {
  enabled = true
}
