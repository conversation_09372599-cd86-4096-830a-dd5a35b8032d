locals {
  filesystems = {
    "nango_secrets" = {
      encrypted = true

      # File system policy
      attach_policy = false

      # Backup policy
      enable_backup_policy = true

      mount_targets = {
        "us-east-1a" = {
          subnet_id       = module.vpc["prod"].private_subnets[0]
          security_groups = [module.security_group["prod-self"].security_group_id]
        }
        "us-east-1b" = {
          subnet_id       = module.vpc["prod"].private_subnets[1]
          security_groups = [module.security_group["prod-self"].security_group_id]
        }
      }

      tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-22737", Creator = "<EMAIL>", Tier = "Prod", Component = "EFS", CreateDate = "20240305" })}"
    }
  }
}

module "efs" {
  source = "terraform-aws-modules/efs/aws"

  for_each = local.filesystems

  name                                  = each.key
  creation_token                        = try(each.value.creation_token, null)
  encrypted                             = true
  kms_key_arn                           = try(each.value.kms_key_arn, null)
  performance_mode                      = try(each.value.performance_mode, "generalPurpose")
  throughput_mode                       = try(each.value.throughput_mode, "bursting")
  provisioned_throughput_in_mibps       = try(each.value.provisioned_throughput_in_mibps, null)
  lifecycle_policy                      = try(each.value.lifecycle_policy, { transition_to_ia = "AFTER_30_DAYS" })
  attach_policy                         = try(each.value.attach_policy, true)
  bypass_policy_lockout_safety_check    = try(each.value.bypass_policy_lockout_safety_check, false)
  policy_statements                     = try(each.value.policy_statements, [])
  mount_targets                         = try(each.value.mount_targets, {})
  create_security_group                 = try(each.value.create_security_group, false)
  security_group_description            = try(each.value.security_group_description, null)
  security_group_vpc_id                 = try(each.value.security_group_vpc_id, null)
  security_group_rules                  = try(each.value.security_group_rules, {})
  access_points                         = try(each.value.access_points, {})
  enable_backup_policy                  = try(each.value.enable_backup_policy, true)
  create_replication_configuration      = try(each.value.create_replication_configuration, false)
  replication_configuration_destination = try(each.value.replication_configuration_destination, {})
  tags                                  = try(each.value.tags, {})
}
